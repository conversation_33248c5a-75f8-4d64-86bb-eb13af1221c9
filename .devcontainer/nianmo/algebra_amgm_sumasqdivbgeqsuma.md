# Proof Tree: Cyclic Sum Inequality via Cauchy-<PERSON>hwarz

## ROOT_001 [ROOT]
**Goal**: Prove that for all positive reals $a,b,c,d$: $\frac{a^2}{b} + \frac{b^2}{c} + \frac{c^2}{d} + \frac{d^2}{a} \geq a + b + c + d$

**Parent Node**: None

**Strategy**: Apply <PERSON><PERSON><PERSON>'s Engel form (Tit<PERSON>'s Lemma) as the main approach

---

## STRATEGY_001 [STRATEGY]
**Goal**: Use Cauchy-Schwarz inequality in Engel form (Titu's Lemma)

**Parent Node**: ROOT_001

**Detailed Plan**: 
1. Apply Titu's Lemma: $\sum \frac{x_i^2}{y_i} \geq \frac{(\sum x_i)^2}{\sum y_i}$
2. Set $(x_1,x_2,x_3,x_4) = (a,b,c,d)$ and $(y_1,y_2,y_3,y_4) = (b,c,d,a)$
3. Show that $\frac{(a+b+c+d)^2}{b+c+d+a} = a+b+c+d$

**Strategy**: Use mathlib's Cauchy-Schwarz inequality theorem

---

## SUBGOAL_001 [TO_EXPLORE]
**Goal**: Establish formal statement with proper types and assumptions

**Parent Node**: STRATEGY_001

**Strategy**: Define theorem with `(a b c d : ℝ)` and `(ha : 0 < a) (hb : 0 < b) (hc : 0 < c) (hd : 0 < d)`

---

## SUBGOAL_002 [TO_EXPLORE]
**Goal**: Apply Cauchy-Schwarz in Engel form from mathlib

**Parent Node**: STRATEGY_001

**Strategy**: Use `Real.sum_div_pow_le_iff_div_pow_le` or similar Cauchy-Schwarz variant from mathlib

---

## SUBGOAL_003 [TO_EXPLORE]
**Goal**: Simplify the fraction $\frac{(a+b+c+d)^2}{a+b+c+d}$

**Parent Node**: STRATEGY_001

**Strategy**: Use algebraic simplification to show this equals $a+b+c+d$

---

## SUBGOAL_004 [TO_EXPLORE]
**Goal**: Handle positivity conditions for division

**Parent Node**: STRATEGY_001

**Strategy**: Use positivity assumptions to ensure all divisions are well-defined
